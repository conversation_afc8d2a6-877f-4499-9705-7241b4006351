<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="楼宇名称" prop="name" label-width="170">
        <el-input v-model="queryParams.name" placeholder="请输入楼宇名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['building:building:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['building:building:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['building:building:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['building:building:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['building:building:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="buildingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="80" align="center" />
      <!-- <el-table-column label="序列" align="center" width="80">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column> -->
      <el-table-column label="楼宇编号" align="center" prop="id"  />
      <el-table-column label="楼宇名称" align="center" prop="name" width="200" />
      <el-table-column label="楼层数" align="center" prop="floorNum" width="100" />
      <el-table-column label="进驻企业数量" align="center" prop="companiesNum" width="130" />
      <el-table-column label="经度" align="center" prop="longitude" width="100" />
      <el-table-column label="纬度" align="center" prop="latitude" width="100" />
      <el-table-column label="总占地面积㎡" align="center" prop="coverArea" width="130" />
      <el-table-column label="总建筑面积㎡" align="center" prop="buildingArea" width="130" />
      <el-table-column label="总可招商面积㎡" align="center" prop="attractInvestmentArea" width="130" />
      <el-table-column label="总商业面积㎡" align="center" prop="businessArea" width="130" />
      <el-table-column label="总办公面积㎡" align="center" prop="officialArea" width="130" />
      <el-table-column label="商业可招商面积㎡" align="center" prop="isBusinessArea" width="170" />
      <el-table-column label="办公可招商面积㎡" align="center" prop="isOfficialArea" width="170" />
      <el-table-column label="主导行业" align="center" prop="leadingIndustry" width="100" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['building:building:edit']"
            >修改</el-button
          >
          <el-button link type="primary" icon="OfficeBuilding" @click="handleFloors(scope.row)"
            >楼层</el-button
          >
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['building:building:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改楼宇信息对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="buildingRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="25">
            <el-form-item label="楼宇名称" prop="name" label-width="170">
              <el-input v-model="form.name" placeholder="请输入楼宇名称" style="width: 280px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
<el-form-item label="父id" prop="parentId" label-width="170">
   <el-input v-model="form.parentId" placeholder="请输入父id"  style="width:280px" />
</el-form-item>
</el-col> -->
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude" label-width="170">
              <el-input v-model="form.longitude" placeholder="请输入经度" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude" label-width="170">
              <el-input v-model="form.latitude" placeholder="请输入纬度" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总占地面积" prop="coverArea" label-width="170">
              <el-input v-model="form.coverArea" placeholder="请输入总占地面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总建筑面积" prop="buildingArea" label-width="170">
              <el-input v-model="form.buildingArea" placeholder="请输入总建筑面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总可招商面积" prop="attractInvestmentArea" label-width="170">
              <el-input v-model="form.attractInvestmentArea" placeholder="请输入总可招商面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总商业面积" prop="businessArea" label-width="170">
              <el-input v-model="form.businessArea" placeholder="请输入总商业面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总办公面积" prop="officialArea" label-width="170">
              <el-input v-model="form.officialArea" placeholder="请输入总办公面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商业可招商面积" prop="isBusinessArea" label-width="170">
              <el-input v-model="form.isBusinessArea" placeholder="请输入商业可招商面积" style="width: 280px" />
            </el-form-item>
          </el-col>
          <el-col :span="25">
            <el-form-item label="办公可招商面积" prop="isOfficialArea" label-width="170">
              <el-input v-model="form.isOfficialArea" placeholder="请输入办公可招商面积" style="width: 280px" />
            </el-form-item>
          </el-col>          
        </el-row>
        <el-row>
          <el-col :span="25">
            <el-form-item label="主导行业" prop="leadingIndustry" label-width="170">
              <el-input v-model="form.leadingIndustry" placeholder="请输入主导行业" style="width: 770px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="25">
            <el-form-item label="楼栋全景图" prop="attrImg" label-width="170">
              <image-upload
                v-model="form.attrImg"
                :limit="1"
                :file-size="10"
                :file-type="['jpg', 'jpeg', 'png']"
                :upload-url="panoramaUploadUrl"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="building">
import { getToken } from "@/utils/auth";
import { listBuilding, getBuilding, delBuilding, addBuilding, updateBuilding } from "@/api/building/building";
const { proxy } = getCurrentInstance();
const buildingList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const daterangeCreateTime = ref([]);
const daterangeUpdateTime = ref([]);
// 全景图上传地址
const panoramaUploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/file/upload");
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
  },
  rules: ref({}),
});
const { queryParams, form, rules } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/building/building/importData",
}); /** 查询楼宇信息列表 */
function getList() {
  loading.value = true;
  listBuilding(queryParams.value).then((response) => {
    buildingList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    parentId: null,
    longitude: null,
    latitude: null,
    coverArea: null,
    buildingArea: null,
    attractInvestmentArea: null,
    businessArea: null,
    officialArea: null,
    isBusinessArea: null,
    isOfficialArea: null,
    leadingIndustry: null,
    companiesNum: null,
    remark: null,
  };
  proxy.resetForm("buildingRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加楼宇信息";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getBuilding(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改楼宇信息";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["buildingRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateBuilding(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBuilding(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm("是否确认删除楼宇编号为" + _ids + "的数据项？")
    .then(function () {
      return delBuilding(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 楼层管理按钮操作 */
function handleFloors(row) {
  proxy.$router.push('/building/floor/' + row.id);
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "building/building/export",
    {
      ...queryParams.value,
    },
    `building_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "楼宇信息导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("building/building/downTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
getList();
</script>
